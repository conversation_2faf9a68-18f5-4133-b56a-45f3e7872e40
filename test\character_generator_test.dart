import 'package:flutter_test/flutter_test.dart';
import 'package:novel_app/langchain/prompts/character_generation_prompts.dart';

void main() {
  group('CharacterGenerationPrompts Tests', () {
    test('should build simple character prompt correctly', () {
      final prompt = CharacterGenerationPrompts.buildSimpleCharacterPrompt(
        title: '测试小说',
        genre: '玄幻',
        background: '修仙世界',
        characterCount: 2,
        preferredTypes: ['主角：故事的核心人物', '反派：主要对手'],
      );

      expect(prompt, contains('测试小说'));
      expect(prompt, contains('玄幻'));
      expect(prompt, contains('修仙世界'));
      expect(prompt, contains('2个角色'));
      expect(prompt, contains('主角：故事的核心人物'));
      expect(prompt, contains('反派：主要对手'));
    });

    test('should extract JSON from valid response', () {
      const validResponse = '''
[
  {
    "name": "张三",
    "gender": "男",
    "age": "25岁",
    "bodyDescription": "身材高大",
    "personalityTraits": "勇敢",
    "background": "平民出身"
  }
]
''';

      final result = CharacterGenerationPrompts.extractJsonFromResponse(validResponse);
      expect(result, isNotNull);
      expect(result, contains('"name": "张三"'));
    });

    test('should extract JSON from code block response', () {
      const codeBlockResponse = '''
这是一些解释文字

```json
[
  {
    "name": "李四",
    "gender": "女",
    "age": "20岁",
    "bodyDescription": "身材娇小",
    "personalityTraits": "聪明",
    "background": "书香门第"
  }
]
```

这是一些结尾文字
''';

      final result = CharacterGenerationPrompts.extractJsonFromResponse(codeBlockResponse);
      expect(result, isNotNull);
      expect(result, contains('"name": "李四"'));
    });

    test('should return null for invalid response', () {
      const invalidResponse = '这是一个无效的响应，没有JSON数据';

      final result = CharacterGenerationPrompts.extractJsonFromResponse(invalidResponse);
      expect(result, isNull);
    });

    test('should create error character JSON', () {
      final errorJson = CharacterGenerationPrompts.createErrorCharacterJson(
        '测试错误信息',
        count: 2,
      );

      expect(errorJson, contains('"name":"生成失败1"'));
      expect(errorJson, contains('"name":"生成失败2"'));
      expect(errorJson, contains('测试错误信息'));
    });

    test('should build error recovery prompt', () {
      final prompt = CharacterGenerationPrompts.buildErrorRecoveryPrompt(
        title: '测试小说',
        genre: '玄幻',
        characterCount: 1,
      );

      expect(prompt, contains('测试小说'));
      expect(prompt, contains('玄幻'));
      expect(prompt, contains('1个简单角色'));
    });
  });
}
